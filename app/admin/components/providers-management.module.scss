/* 服务商管理页面样式 */
.providers-management {
  padding: 0;
  max-width: none;
  margin: 0;
}

.page-header {
  margin-bottom: 24px;
  padding: 20px 24px;
  background: var(--second);
  border-bottom: var(--border-in-light);

  h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--black);
    margin: 0 0 8px 0;
  }

  p {
    color: var(--black);
    opacity: 0.7;
    margin: 0;
    font-size: 14px;
  }
}

/* 服务提供商卡片样式 */
.provider-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 0;
  padding: 0 24px 24px 24px;
}

.provider-card {
  border: var(--border-in-light);
  border-radius: 8px;
  background: var(--white);
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.provider-card-active {
  border-color: var(--primary);
  box-shadow: 0 0 0 1px rgba(29, 147, 171, 0.3);
}

.provider-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--second);
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--hover-color);
  }
}

.provider-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.provider-icon {
  font-size: 24px;
  line-height: 1;
}

.provider-name-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.provider-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--black);
}

.provider-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  background: var(--primary);
  color: white;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: badge-appear 0.3s ease;
}

@keyframes badge-appear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.provider-description {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: var(--black);
  opacity: 0.7;
}

.provider-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.provider-toggle {
  display: flex;
  align-items: center;
}

.provider-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary);
}

.collapse-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: var(--black);
  opacity: 0.7;

  &:hover {
    opacity: 1;
    background: var(--hover-color);
  }

  svg {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
    filter: brightness(1.2);
  }

  &.collapsed svg {
    transform: rotate(-90deg);
  }

  // 暗夜模式下的样式
  @media (prefers-color-scheme: dark) {
    color: var(--white);
    opacity: 0.8;
    
    svg {
      filter: brightness(1.5);
    }
    
    &:hover {
      opacity: 1;
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.provider-config {
  border-top: var(--border-in-light);
  background: var(--white);
  overflow: hidden;
  transition: all 0.3s ease;

  &.collapsed {
    max-height: 0;
    opacity: 0;
    padding: 0 16px;
  }

  &.expanded {
    max-height: 1000px;
    opacity: 1;
    padding: 16px;
  }
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-size: 14px;
    font-weight: 500;
    color: var(--black);
  }
}

.form-input {
  padding: 12px;
  border: 1px solid var(--border-in-light);
  border-radius: 8px;
  font-size: 14px;
  background: var(--white);
  color: var(--black);
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(29, 147, 171, 0.2);
  }

  &::placeholder {
    color: var(--black);
    opacity: 0.5;
  }
}

.form-hint {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.save-button, .manage-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-button {
  background: var(--primary);
  color: white;

  &:hover {
    background: var(--primary-dark);
  }
}

.manage-button {
  background: var(--second);
  color: var(--black);
  border: 1px solid var(--border-in-light);

  &:hover {
    background: var(--hover-color);
  }
}

/* 模型配置区域样式 */
.models-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 12px;
  border: 1px solid var(--border-in-light);
  border-radius: 8px;
  background: var(--second);
}

.models-display {
  flex: 1;
  min-height: 24px;
  display: flex;
  align-items: center;
}

.model-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.model-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background: var(--primary);
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.model-tag-more {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background: var(--black);
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  opacity: 0.7;
}

.no-models {
  color: var(--black);
  opacity: 0.6;
  font-size: 14px;
  font-style: italic;
}

.manage-models-button {
  padding: 6px 12px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: var(--primary-dark);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .providers-management {
    padding: 16px;
  }
  
  .page-header {
    h1 {
      font-size: 24px;
    }
  }
  
  .provider-card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .provider-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .provider-controls {
    justify-content: flex-end;
  }
}
