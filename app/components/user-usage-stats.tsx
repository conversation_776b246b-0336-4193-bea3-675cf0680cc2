import React, { useState, useEffect } from "react";
import { useUserStore } from "../store/user";
import { showToast } from "./ui-lib";
import styles from "./user-usage-stats.module.scss";

interface UserUsageData {
  usage: {
    userId: string;
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    totalTokens: number;
    promptTokens: number;
    completionTokens: number;
    totalCost: number;
    successRate: number;
    averageResponseTime: number;
    topModels: Array<{
      model: string;
      calls: number;
      tokens: number;
      cost: number;
    }>;
    dailyUsage: Array<{
      date: string;
      calls: number;
      tokens: number;
      cost: number;
    }>;
  };
  billing: {
    planFee: number;
    usageFee: number;
    totalFee: number;
    tokensUsed: number;
    tokensIncluded: number;
    tokensOverage: number;
    plan: {
      id: string;
      name: string;
      description: string;
      monthlyTokenLimit: number;
      pricePerToken: number;
      monthlyFee: number;
      features: string[];
    };
  };
  limits: {
    withinLimit: boolean;
    tokensUsed: number;
    tokensLimit: number;
    usagePercentage: number;
  };
}

interface UserUsageStatsProps {
  visible: boolean;
  onClose: () => void;
}

export function UserUsageStats({ visible, onClose }: UserUsageStatsProps) {
  const userStore = useUserStore();
  const [data, setData] = useState<UserUsageData | null>(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  useEffect(() => {
    if (visible && userStore.isAuthenticated) {
      loadUsageData();
    }
  }, [visible, dateRange]);

  const loadUsageData = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        planId: "free", // 默认使用免费方案
      });

      const response = await fetch(`/api/user/usage?${params}`, {
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      });

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        showToast(result.error || "获取使用统计失败");
      }
    } catch (error) {
      console.error("Load usage data error:", error);
      showToast("获取使用统计失败");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN");
  };

  const formatCost = (cost: number) => {
    return `☁️${cost.toFixed(2)} 点数`;
  };

  const getUsageColor = (percentage: number) => {
    if (percentage < 50) return "#52c41a";
    if (percentage < 80) return "#faad14";
    return "#ff4d4f";
  };

  if (!visible) return null;

  return (
    <div
      className={styles["modal-overlay"]}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className={styles["modal"]}>
        <div className={styles["modal-header"]}>
          <h2>使用统计</h2>
          <button onClick={onClose} className={styles["close-button"]}>
            ×
          </button>
        </div>

        <div className={styles["modal-body"]}>
          {/* 日期选择 */}
          <div className={styles["date-selector"]}>
            <div className={styles["date-item"]}>
              <label>开始日期</label>
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) =>
                  setDateRange({ ...dateRange, startDate: e.target.value })
                }
              />
            </div>
            <div className={styles["date-item"]}>
              <label>结束日期</label>
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) =>
                  setDateRange({ ...dateRange, endDate: e.target.value })
                }
              />
            </div>
          </div>

          {loading ? (
            <div className={styles["loading"]}>加载中...</div>
          ) : data ? (
            <div className={styles["stats-content"]}>
              {/* 使用量限制 */}
              <div className={styles["usage-limit"]}>
                <div className={styles["limit-header"]}>
                  <h3>当月使用量</h3>
                  <span className={styles["plan-name"]}>
                    {data.billing.plan.name}
                  </span>
                </div>
                <div className={styles["usage-bar"]}>
                  <div
                    className={styles["usage-fill"]}
                    style={{
                      width: `${Math.min(100, data.limits.usagePercentage)}%`,
                      backgroundColor: getUsageColor(
                        data.limits.usagePercentage,
                      ),
                    }}
                  ></div>
                </div>
                <div className={styles["usage-text"]}>
                  {data.limits.tokensUsed.toLocaleString()} /{" "}
                  {data.limits.tokensLimit > 0
                    ? data.limits.tokensLimit.toLocaleString()
                    : "无限制"}{" "}
                  tokens ({data.limits.usagePercentage.toFixed(1)}%)
                </div>
              </div>

              {/* 统计卡片 */}
              <div className={styles["stats-grid"]}>
                <div className={styles["stat-card"]}>
                  <div className={styles["stat-title"]}>总调用次数</div>
                  <div className={styles["stat-value"]}>
                    {data.usage.totalCalls}
                  </div>
                </div>
                <div className={styles["stat-card"]}>
                  <div className={styles["stat-title"]}>成功率</div>
                  <div className={styles["stat-value"]}>
                    {data.usage.successRate.toFixed(1)}%
                  </div>
                </div>
                <div className={styles["stat-card"]}>
                  <div className={styles["stat-title"]}>总Token数</div>
                  <div className={styles["stat-value"]}>
                    {data.usage.totalTokens.toLocaleString()}
                  </div>
                </div>
                <div className={styles["stat-card"]}>
                  <div className={styles["stat-title"]}>总消耗</div>
                  <div className={styles["stat-value"]}>
                    {formatCost(data.usage.totalCost)}
                  </div>
                </div>
              </div>

              {/* 最常用模型 */}
              <div className={styles["section"]}>
                <h3>最常用模型</h3>
                <div className={styles["models-list"]}>
                  {data.usage.topModels.slice(0, 5).map((model, index) => (
                    <div key={model.model} className={styles["model-item"]}>
                      <div className={styles["model-rank"]}>#{index + 1}</div>
                      <div className={styles["model-info"]}>
                        <div className={styles["model-name"]}>
                          {model.model}
                        </div>
                        <div className={styles["model-stats"]}>
                          {model.calls} 次调用 • {model.tokens.toLocaleString()}{" "}
                          tokens • {formatCost(model.cost)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 每日使用趋势 */}
              <div className={styles["section"]}>
                <h3>每日使用趋势</h3>
                <div className={styles["daily-chart"]}>
                  {data.usage.dailyUsage.slice(-7).map((day) => (
                    <div key={day.date} className={styles["day-item"]}>
                      <div className={styles["day-date"]}>
                        {formatDate(day.date)}
                      </div>
                      <div className={styles["day-bar"]}>
                        <div
                          className={styles["day-fill"]}
                          style={{
                            height: `${Math.max(
                              5,
                              (day.calls /
                                Math.max(
                                  ...data.usage.dailyUsage.map((d) => d.calls),
                                )) *
                                100,
                            )}%`,
                          }}
                        ></div>
                      </div>
                      <div className={styles["day-value"]}>{day.calls}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 计费信息 */}
              <div className={styles["section"]}>
                <h3>计费信息</h3>
                <div className={styles["billing-info"]}>
                  <div className={styles["billing-item"]}>
                    <span>方案费用</span>
                    <span>{formatCost(data.billing.planFee)}</span>
                  </div>
                  <div className={styles["billing-item"]}>
                    <span>超量费用</span>
                    <span>{formatCost(data.billing.usageFee)}</span>
                  </div>
                  <div className={styles["billing-item"]}>
                    <span>总费用</span>
                    <span className={styles["total-fee"]}>
                      {formatCost(data.billing.totalFee)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className={styles["no-data"]}>暂无数据</div>
          )}
        </div>
      </div>
    </div>
  );
}
