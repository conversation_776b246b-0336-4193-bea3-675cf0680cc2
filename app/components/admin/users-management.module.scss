.container {
  padding: 0;
  background: transparent;
  min-height: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  padding: 20px 24px;
  background: var(--second);
  border-radius: 0;
  box-shadow: none;
  border: none;
  border-bottom: var(--border-in-light);

  h2 {
    margin: 0;
    color: var(--black);
    font-size: 20px;
    font-weight: 600;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    background-clip: unset;
    display: flex;
    align-items: center;
    gap: 8px;

    &::before {
      content: '👥';
      font-size: 16px;
      -webkit-text-fill-color: initial;
    }
  }
}

.createButton {
  background: var(--primary);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '➕';
    font-size: 16px;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(29, 147, 171, 0.4);
  }
}

.loading {
  text-align: center;
  padding: 60px;
  color: var(--text-secondary);
  font-size: 16px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);

  &::before {
    content: '⏳';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.7;
  }
}

.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid rgba(239, 68, 68, 0.2);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 12px;

  &::before {
    content: '⚠️';
    font-size: 20px;
  }
}

.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
  padding: 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
}

.searchInput {
  flex: 1;
  min-width: 250px;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 14px;
  background: var(--gray-50);
  color: var(--text-primary);
  transition: all 0.3s ease;

  &::placeholder {
    color: var(--text-muted);
  }

  &:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(29, 147, 171, 0.1);
    background: var(--white);
    transform: translateY(-1px);
  }
}

.filterSelect {
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 14px;
  background: var(--gray-50);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;

  &:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(29, 147, 171, 0.1);
    background: var(--white);
    transform: translateY(-1px);
  }
}

.tableContainer {
  overflow-x: auto;
  border: 1px solid var(--border-color);
  border-radius: 16px;
  margin-bottom: 24px;
  background: var(--white);
  box-shadow: var(--card-shadow);
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;

  th {
    background: var(--gray-100);
    padding: 16px 20px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
    z-index: 10;
  }

  td {
    padding: 16px 20px;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
    color: var(--text-primary);
    font-weight: 500;
  }

  tr {
    transition: all 0.2s ease;

    &:hover {
      background: var(--gray-50);
      transform: scale(1.001);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    &:last-child td {
      border-bottom: none;
    }
  }
}

.role {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 4px;

  &.admin {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);

    &::before {
      content: '👑';
      font-size: 12px;
    }
  }

  &.user {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(59, 130, 246, 0.2);

    &::before {
      content: '👤';
      font-size: 12px;
    }
  }
}

.status {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 4px;

  &.enabled {
    background: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(34, 197, 94, 0.2);

    &::before {
      content: '✅';
      font-size: 12px;
    }
  }

  &.disabled {
    background: #fee2e2;
    color: #dc2626;
  }
}

.actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.viewButton,
.enableButton,
.disableButton,
.resetButton,
.deleteButton {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.viewButton {
  background: var(--primary);
  color: white;

  &:hover {
    background: var(--primary-dark);
  }
}

.enableButton {
  background: #10b981;
  color: white;

  &:hover {
    background: #059669;
  }
}

.disableButton {
  background: #f59e0b;
  color: white;

  &:hover {
    background: #d97706;
  }
}

.resetButton {
  background: #6366f1;
  color: white;

  &:hover {
    background: #4f46e5;
  }
}

.deleteButton {
  background: #ef4444;
  color: white;

  &:hover {
    background: #dc2626;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.pageButton {
  padding: 8px 16px;
  border: 1px solid var(--border-color);
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--primary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.pageInfo {
  color: var(--gray-600);
  font-size: 14px;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .filters {
    flex-direction: column;
  }

  .searchInput {
    min-width: auto;
  }

  .table {
    font-size: 12px;

    th,
    td {
      padding: 8px 4px;
    }
  }

  .actions {
    flex-direction: column;
    gap: 4px;
  }

  .viewButton,
  .enableButton,
  .disableButton,
  .resetButton,
  .deleteButton {
    width: 100%;
    text-align: center;
  }
}
