import React, { useState, useEffect } from "react";
import { List, ListItem, showToast } from "../ui-lib";
import { IconButton } from "../button";
import { useUserStore } from "../../store/user";
import styles from "./model-pricing-management.module.scss";

interface ModelPricing {
  id: string;
  model: string;
  displayName: string;
  provider: string;
  providerDisplayName: string;
  inputTokenPrice: number;
  outputTokenPrice: number;
  enabled: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

interface DefaultPricing {
  inputTokenPrice: number;
  outputTokenPrice: number;
}

export function ModelPricingManagement() {
  const [modelPricings, setModelPricings] = useState<ModelPricing[]>([]);
  const [defaultPricing, setDefaultPricing] = useState<DefaultPricing>({
    inputTokenPrice: 1.0,
    outputTokenPrice: 2.0,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editingPricing, setEditingPricing] = useState<ModelPricing | null>(
    null,
  );
  const [showDefaultPricingModal, setShowDefaultPricingModal] = useState(false);
  const userStore = useUserStore();

  // 加载默认定价配置
  const loadDefaultPricing = async () => {
    try {
      const response = await fetch("/api/admin/default-pricing", {
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setDefaultPricing(result.data);
        }
      }
    } catch (error) {
      console.error("Load default pricing error:", error);
    }
  };

  // 加载模型定价数据
  const loadModelPricings = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/model-pricing", {
        headers: {
          Authorization: `Bearer ${userStore.token}`,
        },
      });

      if (!response.ok) {
        throw new Error("获取模型定价失败");
      }

      const result = await response.json();
      if (result.success) {
        setModelPricings(result.data);
      } else {
        throw new Error(result.error || "获取模型定价失败");
      }
    } catch (error) {
      console.error("Load model pricings error:", error);
      showToast("获取模型定价失败");
    } finally {
      setLoading(false);
    }
  };

  // 保存默认定价
  const saveDefaultPricing = async (newDefaultPricing: DefaultPricing) => {
    try {
      setSaving(true);
      const response = await fetch("/api/admin/default-pricing", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userStore.token}`,
        },
        body: JSON.stringify(newDefaultPricing),
      });

      if (!response.ok) {
        throw new Error("更新默认定价失败");
      }

      const result = await response.json();
      if (result.success) {
        showToast("默认定价已更新");
        setDefaultPricing(newDefaultPricing);
        setShowDefaultPricingModal(false);
      } else {
        throw new Error(result.error || "更新默认定价失败");
      }
    } catch (error) {
      console.error("Save default pricing error:", error);
      showToast("更新默认定价失败");
    } finally {
      setSaving(false);
    }
  };

  // 保存模型定价
  const saveModelPricing = async (pricing: ModelPricing) => {
    try {
      setSaving(true);
      const response = await fetch(`/api/admin/model-pricing/${pricing.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userStore.token}`,
        },
        body: JSON.stringify({
          inputTokenPrice: pricing.inputTokenPrice,
          outputTokenPrice: pricing.outputTokenPrice,
          enabled: pricing.enabled,
          description: pricing.description,
        }),
      });

      if (!response.ok) {
        throw new Error("更新模型定价失败");
      }

      const result = await response.json();
      if (result.success) {
        showToast("模型定价已更新");
        await loadModelPricings(); // 重新加载数据
        setEditingPricing(null);
      } else {
        throw new Error(result.error || "更新模型定价失败");
      }
    } catch (error) {
      console.error("Save model pricing error:", error);
      showToast("更新模型定价失败");
    } finally {
      setSaving(false);
    }
  };

  // 按服务商分组模型
  const groupedPricings = modelPricings.reduce(
    (groups, pricing) => {
      const provider = pricing.providerDisplayName;
      if (!groups[provider]) {
        groups[provider] = [];
      }
      groups[provider].push(pricing);
      return groups;
    },
    {} as Record<string, ModelPricing[]>,
  );

  useEffect(() => {
    loadDefaultPricing();
    loadModelPricings();
  }, []);

  if (loading) {
    return (
      <div className={styles["loading-container"]}>
        <div className={styles["loading-text"]}>加载中...</div>
      </div>
    );
  }

  return (
    <div className={styles["model-pricing-management"]}>
      <div className={styles["header"]}>
        <div className={styles["header-content"]}>
          <div>
            <h2>模型定价管理</h2>
            <p>管理已启用服务商的已启用模型的输入输出价格设置</p>
          </div>
          <div className={styles["header-actions"]}>
            <IconButton
              text="默认价格设置"
              onClick={() => setShowDefaultPricingModal(true)}
            />
          </div>
        </div>
        <div className={styles["default-pricing-info"]}>
          <span>当前默认价格：</span>
          <span>
            输入 ☁️{defaultPricing.inputTokenPrice.toFixed(2)} 点数/1K tokens
          </span>
          <span>
            输出 ☁️{defaultPricing.outputTokenPrice.toFixed(2)} 点数/1K tokens
          </span>
        </div>
      </div>

      <div className={styles["content"]}>
        {Object.entries(groupedPricings).map(([provider, pricings]) => (
          <div key={provider} className={styles["provider-section"]}>
            <h3 className={styles["provider-title"]}>{provider}</h3>
            <List>
              {pricings.map((pricing) => (
                <ListItem
                  key={pricing.id}
                  title={pricing.displayName}
                  subTitle={
                    <div className={styles["pricing-info"]}>
                      <span>
                        输入: ☁️{pricing.inputTokenPrice.toFixed(2)} 点数/1K
                        tokens
                      </span>
                      <span>
                        输出: ☁️{pricing.outputTokenPrice.toFixed(2)} 点数/1K
                        tokens
                      </span>
                      <span className={styles["status"]}>
                        {pricing.enabled ? "✅ 启用" : "❌ 禁用"}
                      </span>
                    </div>
                  }
                >
                  <IconButton
                    text="编辑"
                    onClick={() => setEditingPricing(pricing)}
                  />
                </ListItem>
              ))}
            </List>
          </div>
        ))}

        {Object.keys(groupedPricings).length === 0 && (
          <div className={styles["empty-state"]}>
            <div className={styles["empty-icon"]}>💰</div>
            <h3>暂无模型定价数据</h3>
            <p>请先在服务商管理中启用服务商和模型</p>
          </div>
        )}
      </div>

      {/* 编辑定价弹窗 */}
      {editingPricing && (
        <PricingEditModal
          pricing={editingPricing}
          onSave={saveModelPricing}
          onCancel={() => setEditingPricing(null)}
          saving={saving}
        />
      )}

      {/* 默认价格设置弹窗 */}
      {showDefaultPricingModal && (
        <DefaultPricingModal
          defaultPricing={defaultPricing}
          onSave={saveDefaultPricing}
          onCancel={() => setShowDefaultPricingModal(false)}
          saving={saving}
        />
      )}
    </div>
  );
}

interface PricingEditModalProps {
  pricing: ModelPricing;
  onSave: (pricing: ModelPricing) => void;
  onCancel: () => void;
  saving: boolean;
}

function PricingEditModal({
  pricing,
  onSave,
  onCancel,
  saving,
}: PricingEditModalProps) {
  const [editedPricing, setEditedPricing] = useState<ModelPricing>({
    ...pricing,
  });

  const handleSave = () => {
    onSave(editedPricing);
  };

  return (
    <div className="modal-mask">
      <div className={styles["edit-modal"]}>
        <div className={styles["modal-header"]}>
          <h3>编辑 {editedPricing.displayName} 定价</h3>
        </div>

        <div className={styles["modal-content"]}>
          <List>
            <ListItem title="模型名称">
              <span>{editedPricing.displayName}</span>
            </ListItem>
            <ListItem title="服务商">
              <span>{editedPricing.providerDisplayName}</span>
            </ListItem>
            <ListItem title="输入Token价格 (☁️点数/1K tokens)">
              <input
                type="number"
                step="0.01"
                min="0"
                value={editedPricing.inputTokenPrice}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    inputTokenPrice: parseFloat(e.target.value) || 0,
                  })
                }
                className={styles["price-input"]}
              />
            </ListItem>
            <ListItem title="输出Token价格 (☁️点数/1K tokens)">
              <input
                type="number"
                step="0.01"
                min="0"
                value={editedPricing.outputTokenPrice}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    outputTokenPrice: parseFloat(e.target.value) || 0,
                  })
                }
                className={styles["price-input"]}
              />
            </ListItem>
            <ListItem title="启用状态">
              <input
                type="checkbox"
                checked={editedPricing.enabled}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    enabled: e.target.checked,
                  })
                }
                className={styles["checkbox-input"]}
              />
            </ListItem>
            <ListItem title="描述">
              <input
                type="text"
                value={editedPricing.description || ""}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    description: e.target.value,
                  })
                }
                placeholder="可选的描述信息"
                className={styles["text-input"]}
              />
            </ListItem>
          </List>
        </div>

        <div className={styles["modal-actions"]}>
          <IconButton text="取消" onClick={onCancel} />
          <IconButton
            text={saving ? "保存中..." : "保存"}
            type="primary"
            onClick={handleSave}
            disabled={saving}
          />
        </div>
      </div>
    </div>
  );
}

interface DefaultPricingModalProps {
  defaultPricing: DefaultPricing;
  onSave: (pricing: DefaultPricing) => void;
  onCancel: () => void;
  saving: boolean;
}

function DefaultPricingModal({
  defaultPricing,
  onSave,
  onCancel,
  saving,
}: DefaultPricingModalProps) {
  const [editedPricing, setEditedPricing] = useState<DefaultPricing>({
    ...defaultPricing,
  });

  const handleSave = () => {
    onSave(editedPricing);
  };

  return (
    <div className="modal-mask">
      <div className={styles["edit-modal"]}>
        <div className={styles["modal-header"]}>
          <h3>默认价格设置</h3>
        </div>

        <div className={styles["modal-content"]}>
          <List>
            <ListItem title="说明">
              <span>设置新模型的默认价格，已配置的模型不受影响</span>
            </ListItem>
            <ListItem title="默认输入Token价格 (☁️点数/1K tokens)">
              <input
                type="number"
                step="0.01"
                min="0"
                value={editedPricing.inputTokenPrice}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    inputTokenPrice: parseFloat(e.target.value) || 0,
                  })
                }
                className={styles["price-input"]}
              />
            </ListItem>
            <ListItem title="默认输出Token价格 (☁️点数/1K tokens)">
              <input
                type="number"
                step="0.01"
                min="0"
                value={editedPricing.outputTokenPrice}
                onChange={(e) =>
                  setEditedPricing({
                    ...editedPricing,
                    outputTokenPrice: parseFloat(e.target.value) || 0,
                  })
                }
                className={styles["price-input"]}
              />
            </ListItem>
          </List>
        </div>

        <div className={styles["modal-actions"]}>
          <IconButton text="取消" onClick={onCancel} />
          <IconButton
            text={saving ? "保存中..." : "保存"}
            type="primary"
            onClick={handleSave}
            disabled={saving}
          />
        </div>
      </div>
    </div>
  );
}
