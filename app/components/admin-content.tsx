import React, { useEffect, useMemo } from "react";
import { useLocation } from "react-router-dom";
import { useAdminStore, AdminView } from "../store/admin";
import { AdminDashboard } from "./admin/admin-dashboard";
import { ProvidersManagement } from "../admin/components/providers-management";
import { ModelPricingManagement } from "./admin/model-pricing-management";
import { UsersManagement } from "./admin/users-management";
import { LogsManagement } from "./admin/logs-management";
import { AdminGuard } from "./admin-guard";
import { Path } from "../constant";
import styles from "./admin-content.module.scss";

function SystemSettings() {
  return (
    <div className={styles["placeholder-content"]}>
      <div className={styles["placeholder-icon"]}>⚙️</div>
      <h2>系统设置</h2>
      <p>系统设置功能正在开发中...</p>
    </div>
  );
}

interface AdminContentProps {
  className?: string;
}

export function AdminContent({ className }: AdminContentProps) {
  const adminStore = useAdminStore();
  const location = useLocation();

  // 路径到视图的映射
  const pathToView = useMemo(
    (): Record<string, AdminView> => ({
      [Path.AdminDashboard]: "dashboard",
      [Path.AdminProviders]: "providers",
      [Path.AdminModelPricing]: "model-pricing",
      [Path.AdminUsers]: "users",
      [Path.AdminLogs]: "logs",
      [Path.AdminSettings]: "settings",
    }),
    [],
  );

  // 根据当前路径自动设置管理视图
  useEffect(() => {
    const currentView = pathToView[location.pathname];
    if (currentView) {
      // 确保进入管理模式
      if (!adminStore.isAdminMode) {
        adminStore.enterAdminMode();
      }
      // 设置当前视图
      if (currentView !== adminStore.currentView) {
        adminStore.setCurrentView(currentView);
      }
    }
  }, [location.pathname, adminStore, pathToView]);

  const renderContent = () => {
    switch (adminStore.currentView) {
      case "dashboard":
        return <AdminDashboard />;
      case "providers":
        return (
          <div className={styles["content-wrapper"]}>
            <div className={styles["content-header"]}>
              <h1 className={styles["content-title"]}>服务商管理</h1>
              <p className={styles["content-subtitle"]}>
                配置和管理AI服务商的API密钥
              </p>
            </div>
            <div className={styles["content-body"]}>
              <ProvidersManagement />
            </div>
          </div>
        );
      case "model-pricing":
        return (
          <div className={styles["content-wrapper"]}>
            <div className={styles["content-body"]}>
              <ModelPricingManagement />
            </div>
          </div>
        );
      case "users":
        return <UsersManagement />;
      case "logs":
        return <LogsManagement />;
      case "settings":
        return <SystemSettings />;
      default:
        return <AdminDashboard />;
    }
  };

  return (
    <AdminGuard>
      <div className={`${styles["admin-content"]} ${className || ""}`}>
        {renderContent()}
      </div>
    </AdminGuard>
  );
}
