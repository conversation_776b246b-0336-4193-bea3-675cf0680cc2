.settings {
  padding: 20px;
  overflow: auto;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.settings-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
  padding: 12px;
  background: var(--second);
  border-radius: 12px;
  border: var(--border-in-light);
}

.settings-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: var(--black);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  white-space: nowrap;

  &:hover {
    background: var(--hover-color);
  }
}

.settings-tab-active {
  background: var(--primary);
  color: white;

  &:hover {
    background: var(--primary);
  }
}

.tab-icon {
  font-size: 16px;
  line-height: 1;
}

.tab-label {
  font-weight: 500;
}

.settings-content {
  flex: 1;
  overflow: auto;
}

/* 服务提供商卡片样式 */
.provider-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 20px 0;
}

.provider-card {
  border: var(--border-in-light);
  border-radius: 12px;
  background: var(--white);
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.provider-card-active {
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(29, 147, 171, 0.2);
}

.provider-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--second);
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--hover-color);
  }
}

.provider-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.provider-icon {
  font-size: 24px;
  line-height: 1;
}

.provider-name-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.provider-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--black);
}

.provider-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  background: var(--primary);
  color: white;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  animation: badge-appear 0.3s ease;
}

@keyframes badge-appear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.provider-description {
  margin: 4px 0 0 0;
  font-size: 12px;
  color: var(--black);
  opacity: 0.7;
}

.provider-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.provider-toggle {
  display: flex;
  align-items: center;
}

.provider-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary);
}

.collapse-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: var(--black);
  opacity: 0.7;

  &:hover {
    opacity: 1;
    background: var(--hover-color);
  }

  svg {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
    filter: brightness(1.2);
  }

  &.collapsed svg {
    transform: rotate(-90deg);
  }

  // 暗夜模式下的样式
  @media (prefers-color-scheme: dark) {
    color: var(--white);
    opacity: 0.8;
    
    svg {
      filter: brightness(1.5);
    }
    
    &:hover {
      opacity: 1;
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.provider-config {
  border-top: var(--border-in-light);
  background: var(--white);
  overflow: hidden;
  transition: all 0.3s ease;

  &.collapsed {
    max-height: 0;
    opacity: 0;
    padding: 0 16px;
  }

  &.expanded {
    max-height: 1000px;
    opacity: 1;
    padding: 16px;
  }

  .list {
    margin: 0;
    border: none;
    background: transparent;
    box-shadow: none;
  }

  .list-item {
    padding: 12px 0;
    border-bottom: var(--border-in-light);

    &:last-child {
      border-bottom: none;
    }

    &:first-child {
      padding-top: 8px;
    }
  }
}

/* 启用模型列表样式 */
.enabled-models {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
  width: 100%;
  min-height: 40px; // 确保有足够的最小高度
}

.model-list {
  flex: 1;
  min-width: 0;
  padding: 4px 0; // 添加一些垂直间距
}

.model-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.model-tag {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background: var(--primary);
  color: white;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  line-height: 1.2;
  gap: 6px;
  // 移除文本截断，让标签自然显示完整内容
  // overflow: hidden;
  // text-overflow: ellipsis;
  // max-width: 120px;
}

.no-models {
  color: var(--black);
  opacity: 0.6;
  font-size: 14px;
  font-style: italic;
}

.manage-button {
  padding: 8px 16px;
  border: 1px solid var(--primary);
  border-radius: 6px;
  background: var(--white);
  color: var(--primary);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
  align-self: flex-start; // 与模型标签顶部对齐
  min-height: 32px; // 确保按钮有合适的高度

  &:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(29, 147, 171, 0.2);
  }
}

.avatar {
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.edit-prompt-modal {
  display: flex;
  flex-direction: column;

  .edit-prompt-title {
    max-width: unset;
    margin-bottom: 20px;
    text-align: left;
  }
  .edit-prompt-content {
    max-width: unset;
  }
}

.user-prompt-modal {
  min-height: 40vh;

  .user-prompt-search {
    width: 100%;
    max-width: 100%;
    margin-bottom: 10px;
    background-color: var(--gray);
  }

  .user-prompt-list {
    border: var(--border-in-light);
    border-radius: 10px;

    .user-prompt-item {
      display: flex;
      justify-content: space-between;
      padding: 10px;

      &:not(:last-child) {
        border-bottom: var(--border-in-light);
      }

      .user-prompt-header {
        max-width: calc(100% - 100px);

        .user-prompt-title {
          font-size: 14px;
          line-height: 2;
          font-weight: bold;
        }
        .user-prompt-content {
          font-size: 12px;
        }
      }

      .user-prompt-buttons {
        display: flex;
        align-items: center;
        column-gap: 2px;

        .user-prompt-button {
          //height: 100%;
          padding: 7px;
        }
      }
    }
  }
}

.subtitle-button {
  button {
    overflow:visible ;
  }
}

/* 自定义服务商添加按钮样式 */
.add-custom-provider {
  margin-top: 12px;
}

.add-custom-provider-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 2px dashed var(--border-in-light);
  border-radius: 12px;
  background: transparent;
  color: var(--black);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;

  &:hover {
    border-color: var(--primary);
    background: rgba(29, 147, 171, 0.05);
  }
}

.add-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  font-size: 20px;
  font-weight: bold;
}

.add-text {
  text-align: left;

  h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--black);
  }

  p {
    margin: 0;
    font-size: 14px;
    color: var(--black);
    opacity: 0.7;
  }
}

/* 自定义服务商弹窗样式 */
.modal-container {
  background: var(--white);
  border-radius: 12px;
  width: 90%;
  max-width: 600px; // 增加最大宽度，让输入框有更多空间
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 32px; // 与其他部分保持一致的左右padding
  border-bottom: var(--border-in-light);
  background: var(--second);
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
}

.modal-close-button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: transparent;
  color: var(--black);
  cursor: pointer;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--hover-color);
  }
}

.modal-content {
  padding: 24px 32px; // 增加左右padding，给输入框更多空间
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 20px;

  label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: var(--black);
  }

  input, select {
    width: 100%;
    max-width: none !important; // 覆盖全局样式中的max-width: 50%限制
    padding: 12px 16px; // 增加左右内边距，让输入更舒适
    border: var(--border-in-light);
    border-radius: 8px;
    font-size: 14px;
    background: var(--white);
    color: var(--black);
    transition: border-color 0.2s ease;
    box-sizing: border-box; // 确保padding不会影响总宽度

    &:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: 0 0 0 2px rgba(29, 147, 171, 0.2);
    }

    &.error {
      border-color: var(--red);
    }
  }

  select {
    cursor: pointer;
  }
}

.error-message {
  margin-top: 4px;
  font-size: 12px;
  color: var(--red);
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px 32px; // 与modal-content保持一致的左右padding
  border-top: var(--border-in-light);
  background: var(--second);
}

.cancel-button, .confirm-button {
  flex: 1;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background: transparent;
  color: var(--black);
  border: var(--border-in-light);

  &:hover {
    background: var(--hover-color);
  }
}

.confirm-button {
  background: var(--primary);
  color: white;

  &:hover {
    background: var(--primary-dark);
  }
}

// 模型定价管理器样式
.model-pricing-manager {
  max-height: 70vh;
  overflow-y: auto;

  .loading {
    text-align: center;
    padding: 40px;
    color: var(--black);
  }

  .pricing-list {
    .provider-group {
      margin-bottom: 24px;

      .provider-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--black);
        margin-bottom: 12px;
        padding: 8px 12px;
        background: var(--second);
        border-radius: 8px;
        border: var(--border-in-light);
      }

      .pricing-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .pricing-status {
          font-size: 16px;
          line-height: 1;
        }
      }
    }
  }
}

.pricing-edit-form {
  .list-item {
    input[type="number"],
    input[type="text"] {
      width: 200px;
      padding: 6px 12px;
      border: 1px solid var(--border-in-light);
      border-radius: 6px;
      background: var(--white);
      color: var(--black);
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: var(--primary);
      }
    }

    input[type="checkbox"] {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
}
