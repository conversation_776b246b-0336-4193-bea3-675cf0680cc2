import { create } from "zustand";
import { persist } from "zustand/middleware";

export type AdminView =
  | "dashboard"
  | "providers"
  | "users"
  | "logs"
  | "settings"
  | "model-pricing";

interface AdminState {
  // 是否处于管理模式
  isAdminMode: boolean;
  // 当前选中的管理视图
  currentView: AdminView;

  // Actions
  enterAdminMode: () => void;
  exitAdminMode: () => void;
  setCurrentView: (view: AdminView) => void;
  toggleAdminMode: () => void;
}

export const useAdminStore = create<AdminState>()(
  persist(
    (set, get) => ({
      isAdminMode: false,
      currentView: "dashboard",

      enterAdminMode: () => {
        set({ isAdminMode: true, currentView: "dashboard" });
      },

      exitAdminMode: () => {
        set({ isAdminMode: false });
      },

      setCurrentView: (view: AdminView) => {
        set({ currentView: view });
      },

      toggleAdminMode: () => {
        const { isAdminMode } = get();
        if (isAdminMode) {
          set({ isAdminMode: false });
        } else {
          set({ isAdminMode: true, currentView: "dashboard" });
        }
      },
    }),
    {
      name: "admin-store",
      // 只持久化必要的状态
      partialize: (state) => ({
        currentView: state.currentView,
      }),
    },
  ),
);

// 管理面板菜单项配置
export interface AdminMenuItem {
  id: AdminView;
  name: string;
  icon: string;
  description?: string;
}

export const adminMenuItems: AdminMenuItem[] = [
  {
    id: "dashboard",
    name: "仪表盘",
    icon: "📊",
    description: "系统概览和统计信息",
  },
  {
    id: "providers",
    name: "服务商管理",
    icon: "⚙️",
    description: "配置和管理AI服务商",
  },
  {
    id: "model-pricing",
    name: "模型管理",
    icon: "💰",
    description: "管理模型定价配置",
  },
  {
    id: "users",
    name: "用户管理",
    icon: "👥",
    description: "管理系统用户",
  },
  {
    id: "logs",
    name: "调用日志",
    icon: "📝",
    description: "查看API调用记录",
  },
  {
    id: "settings",
    name: "系统设置",
    icon: "⚙️",
    description: "系统配置和设置",
  },
];
